package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralDocumentHistoryResponseContainer;
import com.adins.esignhubjob.model.custom.privygeneral.PrivyGeneralSignerBean;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsVendor;
import com.adins.esignhubjob.model.table.MsVendorRegisteredUser;
import com.adins.esignhubjob.model.table.MsVendoroftenant;
import com.adins.esignhubjob.model.table.TrBalanceMutation;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDSign;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrDocumentSigningRequest;
import com.adins.esignhubjob.model.table.TrDocumentSigningRequestDetail;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrail;
import com.adins.esignhubjob.model.table.TrSigningProcessAuditTrailDetail;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralDocumentHistoryResponse;
import com.adins.esignhubjob.model.webservice.privygeneral.PrivyGeneralDocumentStatusResponse;
import com.adins.util.IOUtils;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

public class SignPrivyDocumentJob extends BaseJobHandler {

    private static final String AUDIT = "FC_SIGN_PRIVY";
    private static final String CONST_SUCCESS = "Success";

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        String input = IOUtils.toString(inputStream);
        context.getLogger().info("Request ID " + input + ", sign process started");

        Long idDocumentSigningRequest = Long.valueOf(input);
        TrDocumentSigningRequest documentSigningRequest = daoFactory.getDocumentSigningRequestDao().getDocumentSigningRequestNewTran(idDocumentSigningRequest);
        if (null == documentSigningRequest) {
            context.getLogger().warn("Request ID " + input + " is not found");
            return;
        }

        List<TrDocumentSigningRequestDetail> documentSigningRequestDetails = daoFactory.getDocumentSigningRequestDao().getDocumentSigningRequestDetailsNewTran(documentSigningRequest);
        List<Long> idDocumentDList = new ArrayList<>();
        for (TrDocumentSigningRequestDetail requestDetail : documentSigningRequestDetails) {
            idDocumentDList.add(requestDetail.getTrDocumentD().getIdDocumentD());
        }
        
        AmMsuser user = documentSigningRequest.getAmMsuser();
        MsVendor vendor = documentSigningRequest.getMsVendor();
        MsVendorRegisteredUser vendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserNewTran(user, vendor);
        List<TrDocumentDSign> documentDSigns = daoFactory.getDocumentDao().getUnsignedListDocumentDSignNewTran(idDocumentDList, user);

        MsTenant tenant = documentSigningRequest.getTrDocumentH().getMsTenant();
        MsVendoroftenant vendoroftenant = daoFactory.getVendoroftenantDao().getVendoroftenant(tenant.getTenantCode(), Constants.VENDOR_CODE_PRIVY);

        context.getLogger().info(String.format("Request ID %1$s, processing %2$s sign(s) for %3$s", input, documentDSigns.size(), user.getLoginId()));
        checkAllDocumentStatus(documentSigningRequest, documentDSigns, vendorRegisteredUser, vendoroftenant, context);
        
    }

    private void checkAllDocumentStatus(TrDocumentSigningRequest documentSigningRequest, List<TrDocumentDSign> documentDSigns, MsVendorRegisteredUser vendorRegisteredUser, MsVendoroftenant vendoroftenant, Context context) {
        boolean allProcessSuccessful = true;
        for (TrDocumentDSign documentDSign : documentDSigns) {
            // Get the correct vendor registered user for this specific document sign
            // For autosign locations, this will be different from the original request user
            AmMsuser signUser = documentDSign.getAmMsuser();
            MsVendor vendor = vendoroftenant.getMsVendor();
            MsVendorRegisteredUser signUserVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserNewTran(signUser, vendor);

            String signType = (documentDSign.getMsLovByLovAutosign() != null && "AUTO".equals(documentDSign.getMsLovByLovAutosign().getCode())) ? "autosign" : "manual";
            context.getLogger().info(String.format("Request ID %1$s, checking %2$s status for user %3$s (privy_id: %4$s)",
                documentSigningRequest.getIdDocumentSigningRequest(), signType, signUser.getLoginId(),
                signUserVendorRegisteredUser != null ? signUserVendorRegisteredUser.getVendorRegistrationId() : "null"));

            boolean isSuccessful = checkDocumentStatus(documentSigningRequest, documentDSign, signUserVendorRegisteredUser, vendoroftenant, context);
            if (isSuccessful) {
                processSuccessfulSign(documentSigningRequest, documentDSign, vendoroftenant, context);
            } else {
                allProcessSuccessful = false;
                processFailedSign(documentSigningRequest, documentDSign, context);
            }
        }

        documentSigningRequest.setRequestStatus(allProcessSuccessful ? (short) 3 : (short) 2);
        documentSigningRequest.setRequestEnd(new Date());
        documentSigningRequest.setUsrUpd(AUDIT);
        documentSigningRequest.setDtmUpd(new Date());
        daoFactory.getDocumentSigningRequestDao().updateDocumentSigningRequestNewTran(documentSigningRequest);
    }

    private void processSuccessfulSign(TrDocumentSigningRequest documentSigningRequest, TrDocumentDSign documentDSign, MsVendoroftenant vendoroftenant, Context context) {
        
        TrDocumentD document = documentDSign.getTrDocumentD();
        MsVendor vendor = document.getMsVendor();
        MsTenant tenant = document.getMsTenant();
        AmMsuser user = documentSigningRequest.getAmMsuser();
        MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUser(user, vendor);

        documentDSign.setSignDate(new Date());
        if (null != documentDSign.getMsLovByLovAutosign() && "AUTO".equals(documentDSign.getMsLovByLovAutosign().getCode())) {
            documentDSign.setPoaId(vendorUser.getVendorRegistrationId());
        } else {
            documentDSign.setVendorRegistrationId(vendorUser.getVendorRegistrationId());
        }
        
        documentDSign.setUsrUpd(AUDIT);
        documentDSign.setDtmUpd(new Date());
        daoFactory.getDocumentDao().updateDocumentDSignNewTran(documentDSign);
        
        short totalSigned = daoFactory.getDocumentDao().countSignedDocumentNewTran(document);
        boolean signComplete = document.getTotalSign().equals(totalSigned);
        
        if (signComplete) {
            MsLov lovSignStatus = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGN_STATUS, Constants.LOV_CODE_SIGN_STATUS_COMPLETE);
            document.setMsLovByLovSignStatus(lovSignStatus);
            document.setCompletedDate(new Date());
            storeFullySignedDocumentToOss(documentSigningRequest, document, vendoroftenant, context);
        }

        document.setTotalSigned(totalSigned);
        document.setSigningProcess("0");
        document.setUsrUpd(AUDIT);
        document.setDtmUpd(new Date());
        daoFactory.getDocumentDao().updateDocumentDNewTran(document);
        context.getLogger().info(String.format("Request ID %1$s, Kontrak %2$s, Dokumen %3$s, sign process: %4$s/%5$s", documentSigningRequest.getIdDocumentSigningRequest(), document.getTrDocumentH().getRefNumber(), document.getDocumentId(), document.getTotalSigned(), document.getTotalSign()));

        TrDocumentH documentH = document.getTrDocumentH();
        daoFactory.getDocumentDao().updateNativeStringDocumentHSigningProcessNewTrx(documentH, "0", AUDIT);

        String functionResponse = StringUtils.EMPTY;
        if (signComplete) {
            functionResponse = daoFactory.getCommonDao().incrementDocumentHTotalSignedNewTran(documentH.getIdDocumentH());
            context.getLogger().info(String.format("Request ID %1$s, Kontrak %2$s: %3$s", documentSigningRequest.getIdDocumentSigningRequest(), documentH.getRefNumber(), functionResponse));
        }

        MsLov balanceType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_BALANCE_TYPE, Constants.BALANCE_TYPE_CODE_SGN);
        MsLov trxType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_TRX_TYPE, Constants.TRX_TYPE_CODE_USGN);

        long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
        String notes = "Sign " + vendorUser.getSignerRegisteredEmail();

        TrBalanceMutation mutation = new TrBalanceMutation();
        mutation.setTrxNo(String.valueOf(trxNo));
        mutation.setTrxDate(new Date());
        mutation.setRefNo(documentH.getRefNumber());
        mutation.setQty(-1);
        mutation.setMsLovByLovTrxType(trxType);
        mutation.setMsLovByLovBalanceType(balanceType);
        mutation.setUsrCrt(AUDIT);
        mutation.setDtmCrt(new Date());
        mutation.setMsTenant(tenant);
        mutation.setMsVendor(vendor);
        mutation.setTrDocumentD(document);
        mutation.setTrDocumentH(documentH);
        mutation.setAmMsuser(user);
        mutation.setNotes(notes);
        daoFactory.getBalanceMutationDao().insertBalanceMutationNewTran(mutation);

        // Trigger sign callback
        MsLov lovCallbackType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_CALLBACK_TYPE, Constants.LOV_CODE_CALLBACK_TYPE_SIGN_COMPLETE);
        logicFactory.getCallbackLogic().executeCallbackToClient(tenant, lovCallbackType, vendorUser, document, null, CONST_SUCCESS, context);

        // Trigger document complete callback
        if (signComplete) {
            MsLov documentCompleteCallbackLov = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_CALLBACK_TYPE, Constants.LOV_CODE_CALLBACK_TYPE_DOCUMENT_SIGN_COMPLETE);
            logicFactory.getCallbackLogic().executeCallbackToClient(tenant, documentCompleteCallbackLov, null, document, documentH, CONST_SUCCESS, context);
        }
        
        if (functionResponse.endsWith("(callback_process updated)")) {
            // Trigger send sign complete notification
            logicFactory.getCallbackLogic().executeSendSignCompleteNotification(documentH, context);

            // Trigger all document complete callback
            MsLov allDocumentCompleteCallbackLov = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_CALLBACK_TYPE, Constants.LOV_CODE_CALLBACK_TYPE_ALL_DOCUMENT_SIGN_COMPLETE);
            logicFactory.getCallbackLogic().executeCallbackToClient(tenant, allDocumentCompleteCallbackLov, null, document, documentH, CONST_SUCCESS, context);
        }

        boolean autosignNeedSign = autosignNeedSign(document);
        boolean nonAutosignAlreadySigned = allNonAutosignAlreadySigned(document);
        context.getLogger().info(String.format("Request ID %1$s, Kontrak %2$s, Dokumen %3$s, all non-autosign already signed: %4$s, autosign need sign: %5$s", documentSigningRequest.getIdDocumentSigningRequest(), document.getTrDocumentH().getRefNumber(), document.getDocumentId(), nonAutosignAlreadySigned, autosignNeedSign));

        if (nonAutosignAlreadySigned && autosignNeedSign) {
            // Get all unsigned autosign locations for this document
            List<TrDocumentDSign> autosignDocumentDSigns = daoFactory.getDocumentDao().getUnsignedAutosignDocumentDSignListNewTran(document);

            if (!autosignDocumentDSigns.isEmpty()) {
                // Use the first autosign location to get the user (all autosign locations should have the same user)
                TrDocumentDSign firstAutosignLocation = autosignDocumentDSigns.get(0);
                AmMsuser autosignUser = firstAutosignLocation.getAmMsuser();

                context.getLogger().info(String.format("Request ID %1$s, Kontrak %2$s, Dokumen %3$s, creating autosign request for %4$s location(s) for user %5$s",
                    documentSigningRequest.getIdDocumentSigningRequest(), document.getTrDocumentH().getRefNumber(), document.getDocumentId(),
                    autosignDocumentDSigns.size(), autosignUser.getLoginId()));

                TrDocumentSigningRequest signingRequest = new TrDocumentSigningRequest();
                signingRequest.setAmMsuser(autosignUser);
                signingRequest.setTrDocumentD(document);
                signingRequest.setTrDocumentH(documentH);
                signingRequest.setRequestStatus((short) 0);
                signingRequest.setUserRequestIp(Tools.getApplicationIpAddress(context));
                signingRequest.setUserRequestBrowserInformation("No Browser");
                signingRequest.setUserSigningConsentTimestamp(new Date());
                signingRequest.setUsrCrt(AUDIT);
                signingRequest.setDtmCrt(new Date());
                signingRequest.setMsVendor(vendor);
                daoFactory.getDocumentSigningRequestDao().insertDocumentSigningRequestNewTran(signingRequest);

                TrDocumentSigningRequestDetail requestDetail = new TrDocumentSigningRequestDetail();
                requestDetail.setTrDocumentSigningRequest(signingRequest);
                requestDetail.setTrDocumentD(document);
                requestDetail.setUsrCrt(AUDIT);
                requestDetail.setDtmCrt(new Date());
                daoFactory.getDocumentSigningRequestDao().insertDocumentSigningRequestDetailNewTran(requestDetail);
            }
        }

    }

    private void processFailedSign(TrDocumentSigningRequest documentSigningRequest, TrDocumentDSign documentDSign, Context context) {
        
        TrDocumentD document = documentDSign.getTrDocumentD();
        
        document.setSigningProcess("0");
        document.setDtmUpd(new Date());
        document.setUsrUpd(AUDIT);
        daoFactory.getDocumentDao().updateDocumentDNewTran(document);
        
        TrDocumentH documentH = document.getTrDocumentH();
        daoFactory.getDocumentDao().updateNativeStringDocumentHSigningProcessNewTrx(documentH, "0", AUDIT);

        context.getLogger().error(String.format("Request ID %1$s, Kontrak %2$s, Dokumen %3$s, flagged for fail sign", documentSigningRequest.getIdDocumentSigningRequest(), document.getTrDocumentH().getRefNumber(), document.getDocumentId()));
    }

    private boolean allNonAutosignAlreadySigned(TrDocumentD document) {
        short unsignedNonAutosign = daoFactory.getDocumentDao().countUnsignedNonAutosignDocumentDSignNewTran(document);
        return unsignedNonAutosign == 0;
    }

    private boolean autosignNeedSign(TrDocumentD document) {
        short unsignedAutosign = daoFactory.getDocumentDao().countUnsignedAutosignDocumentDSignNewTran(document);
        return unsignedAutosign > 0;
    }

    private void insertAuditTrail(MsVendorRegisteredUser vendorUser, TrDocumentD document, boolean signSuccess, PrivyGeneralDocumentHistoryResponseContainer responseContainer, Context context) {
        
        MsLov lovProcessType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(Constants.LOV_GROUP_SIGNING_PROCESS_TYPE, Constants.SIGNING_PROCESS_TYPE_SIGNING_SUCCESS);

        TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
        trail.setPhoneNoBytea(vendorUser.getPhoneBytea());
        trail.setHashedPhoneNo(vendorUser.getHashedSignerRegisteredPhone());
        trail.setEmail(vendorUser.getSignerRegisteredEmail());
        trail.setAmMsUser(vendorUser.getAmMsuser());
        trail.setMsTenant(document.getMsTenant());
        trail.setMsVendor(vendorUser.getMsVendor());
        trail.setLovProcessType(lovProcessType);
        trail.setResultStatus(signSuccess ? "1" : "0");
        trail.setUsrCrt(context.getRequestId());
        trail.setDtmCrt(new Date());
        daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTrx(trail);

        // Get ID and set notes
        String requestBody = null != responseContainer ? responseContainer.getRequestBody() : null;
        String responseBody = null != responseContainer ? responseContainer.getResponseBody() : null;
        String filepath = logicFactory.getSigningProcessAuditTrailLogic().logProcessRequestResponse(trail, Constants.AUDIT_TRAIL_SUBFOLDER_SIGNING_PROCESS, requestBody, responseBody, false, context);
        trail.setNotes(filepath);
        trail.setUsrUpd(context.getRequestId());
        trail.setDtmUpd(new Date());
        daoFactory.getSigningProcessAuditTrailDao().updateSigningProcessAuditTrailNewTrx(trail);

        TrSigningProcessAuditTrailDetail trailDetail = new TrSigningProcessAuditTrailDetail();
        trailDetail.setSigningProcessAuditTrail(trail);
        trailDetail.setTrDocumentD(document);
        trailDetail.setUsrCrt(context.getRequestId());
        trailDetail.setDtmCrt(new Date());
        daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTrx(trailDetail);
    }

    private boolean checkDocumentStatus(TrDocumentSigningRequest documentSigningRequest, TrDocumentDSign documentDSign, MsVendorRegisteredUser vendorRegisteredUser, MsVendoroftenant vendoroftenant, Context context) {
        int retryAttempts = getRetryAttemptsAmount();
        long delay = getRetryDelay();
        TrDocumentD document = documentDSign.getTrDocumentD();
        TrDocumentH documentH = document.getTrDocumentH();
        PrivyGeneralDocumentHistoryResponseContainer responseContainer = null;

        try {
            while (retryAttempts > 0) {
                
                Thread.sleep(delay);
                context.getLogger().info(String.format("Request ID %1$s, Kontrak %2$s, Dokumen %3$s, check history retry attempts left: %4$s", documentSigningRequest.getIdDocumentSigningRequest(), documentH.getRefNumber(), document.getDocumentId(), retryAttempts));
                
                responseContainer = logicFactory.getPrivyGeneralLogic().checkDocumentHistory(documentSigningRequest, vendoroftenant, documentDSign.getTrDocumentD(), context);
                PrivyGeneralDocumentHistoryResponse response = responseContainer.getResponse();
                boolean isSignSuccessful = isSignSuccessful(response, vendorRegisteredUser);
                if (isSignSuccessful) {
                    insertAuditTrail(vendorRegisteredUser, document, true, responseContainer, context);
                    return true;
                }
                
                retryAttempts -= 1;

            }
            
            context.getLogger().error(String.format("Request ID %1$s, Kontrak %2$s, Dokumen %3$s, check document status failed (max attempts reached)", documentSigningRequest.getIdDocumentSigningRequest(), documentH.getRefNumber(), document.getDocumentId()));
            insertAuditTrail(vendorRegisteredUser, document, false, responseContainer, context);
            return false;

        } catch (InterruptedException e) {

            context.getLogger().error(String.format("Request ID %1$s, thread interrupted", documentSigningRequest.getIdDocumentSigningRequest()));
            Thread.currentThread().interrupt();
            insertAuditTrail(vendorRegisteredUser, document, false, responseContainer, context);
            return false;

        } catch (Exception e) {

            String stacktrace = ExceptionUtils.getStackTrace(e);
            context.getLogger().error(stacktrace);
            context.getLogger().error(String.format("Request ID %1$s, check document history failed with error: %2$s", documentSigningRequest.getIdDocumentSigningRequest(), e.getLocalizedMessage()));
            insertAuditTrail(vendorRegisteredUser, document, false, responseContainer, context);
            return false;

        }
    }

    private boolean isSignSuccessful(PrivyGeneralDocumentHistoryResponse historyResponse, MsVendorRegisteredUser vendorRegisteredUser) {
        List<PrivyGeneralSignerBean> signers = historyResponse.getData().getDocument().get(0).getSigners();
        for (PrivyGeneralSignerBean signer : signers) {
            if (signer.getPrivyId().equalsIgnoreCase(vendorRegisteredUser.getVendorRegistrationId()) && "completed".equals(signer.getStatus())) {
                return true;
            }
        }
        return false;
    }

    private void storeFullySignedDocumentToOss(TrDocumentSigningRequest documentSigningRequest, TrDocumentD document, MsVendoroftenant vendoroftenant, Context context) {
        
        int retryAttempts = getRetryAttemptsAmount();
        long delay = getRetryDelay();
        
        try {

            while (retryAttempts > 0) {

                Thread.sleep(delay);
                context.getLogger().info(String.format("Request ID %1$s, Kontrak %2$s, Dokumen %3$s, check status retry attempts left: %4$s", documentSigningRequest.getIdDocumentSigningRequest(), document.getTrDocumentH().getRefNumber(), document.getDocumentId(), retryAttempts));

                PrivyGeneralDocumentStatusResponse statusResponse = logicFactory.getPrivyGeneralLogic().checkDocumentStatus(documentSigningRequest, vendoroftenant, document, context);
                String base64Document = null;
                if (null != statusResponse.getData() && null != statusResponse.getData().getSignedDocument()) {
                    base64Document = statusResponse.getData().getSignedDocument();
                }

                if (StringUtils.isBlank(base64Document)) {
                    retryAttempts -= 1;
                    continue;
                }
                
                if (base64Document.startsWith(Constants.PDF_PREFIX)) {
                    base64Document = base64Document.substring(Constants.PDF_PREFIX.length());
                }
                
                byte[] documentByteArray = Base64.getDecoder().decode(base64Document);
                logicFactory.getAliyunOssCloudStorageLogic().storeSignedDocument(document, documentByteArray, context);
                return;
            }

            context.getLogger().error(String.format("Request ID %1$s, download signed document failed (URL download not found)", documentSigningRequest.getIdDocumentSigningRequest()));

        } catch (InterruptedException e) {

            context.getLogger().error(String.format("Request ID %1$s, thread interrupted", documentSigningRequest.getIdDocumentSigningRequest()));
            Thread.currentThread().interrupt();

        } catch (Exception e) {
            context.getLogger().error(ExceptionUtils.getStackTrace(e));
            context.getLogger().error(String.format("Request ID %1$s, download signed document failed (exception occurred)", documentSigningRequest.getIdDocumentSigningRequest()));
        }
        
    }

    private int getRetryAttemptsAmount() {
        String gsCode = daoFactory.getGeneralSettingDao().getGsValueByCode("PRIVY_JOB_SIGN_ITERATION");
        if (StringUtils.isBlank(gsCode)) {
            return 20;
        }

        try {
            return Integer.valueOf(gsCode);
        } catch (Exception e) {
            return 20;
        }
    }

    private long getRetryDelay() {
        String gsCode = daoFactory.getGeneralSettingDao().getGsValueByCode("PRIVY_JOB_SIGN_WAIT_TIME");
        if (StringUtils.isBlank(gsCode)) {
            return 1000L;
        }

        try {
            return Integer.valueOf(gsCode);
        } catch (Exception e) {
            return 1000L;
        }
    }
    
}
